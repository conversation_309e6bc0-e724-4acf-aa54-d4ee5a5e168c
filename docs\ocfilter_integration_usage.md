# OCFilter Интеграция - Ръководство за използване

## Общ преглед

OCFilter интеграцията в Multi Feed Syncer модула автоматично създава филтри за продуктите въз основа на атрибутите, извлечени от XML файловете на доставчиците. Това позволява на клиентите да филтрират продуктите по различни характеристики като цвят, размер, мощност и др.

## Активиране на интеграцията

### Стъпка 1: Достъп до настройките
1. Влезте в админ панела на OpenCart
2. Отидете в **Extensions > Modules > MultiFeed Syncer**
3. Кликнете на таба **"Настройки"**

### Стъпка 2: Конфигуриране
1. **Активирай OCFilter интеграция**: Поставете отметка за да активирате функционалността
2. **Максимален брой атрибути на продукт**: Задайте лимит (препоръчително 20-50)
3. Кликнете **"Запази настройките"**

## Как работи автоматичното създаване на филтри

### Извличане на атрибути
Когато Multi Feed Syncer синхронизира продукти, той автоматично:

1. **Извлича атрибути** от XML файла (поле `attributes_data_source`)
2. **Нормализира имената** на атрибутите (премахва специални символи, цифри)
3. **Създава OCFilter опции** за всеки уникален атрибут
4. **Създава стойности** за всяка опция
5. **Свързва продуктите** със съответните стойности

### Пример за обработка

**Входни данни от XML:**
```
Format_A47 → ATX 3.1
razmeri__sh__d__v___mm → 150 x 140 x 86 mm
moshtnost__w → 750 W
A39 → Black
```

**Резултат в OCFilter:**
- **Format** → ATX 3.1
- **Razmeri sh d v mm** → 150 x 140 x 86 mm  
- **Moshtnost w** → 750 W
- **A** → Black

## Управление на филтрите

### Автоматично създаване
- Филтрите се създават автоматично при всяка синхронизация
- Новите атрибути се добавят като нови опции
- Съществуващите опции се преизползват

### Категорийна връзка
- Филтрите се свързват с категориите на продуктите
- Показват се само в съответните категории
- Автоматично наследяване при промяна на категория

### Типове филтри
В момента се създават само **checkbox** филтри. Бъдещи версии ще поддържат:
- **Radio buttons** за единичен избор
- **Slide филтри** за числови стойности
- **Dual slide** за диапазони

## Оптимизация и производителност

### Кеширащ механизъм
- Опциите и стойностите се кешират в паметта
- Намалява броя SQL заявки при синхронизация
- Автоматично изчистване при нужда

### Лимити
- **Максимален брой атрибути**: Конфигурируем лимит за всеки продукт
- **Дължина на имена**: Автоматично съкращаване при нужда
- **Дължина на стойности**: Ограничение до 255 символа

### Пакетна обработка
- Множество продукти се обработват наведнъж
- Оптимизирани SQL заявки за INSERT/UPDATE
- Минимално натоварване на сървъра

## Мониторинг и логове

### Логове за синхронизация
Проверете логовете в **MultiFeed Syncer > Логове** за:
- Брой обработени атрибути
- Създадени нови опции
- Грешки при обработка

### Съобщения в лога
```
MultiFeed Syncer OCFilter: Обработка на атрибути за продукт ID 12345. Общо атрибути: 8
MultiFeed Syncer OCFilter: Създадена нова опция 'Мощност' с ID 15
MultiFeed Syncer OCFilter: Свързани 6 атрибута с продукт ID 12345
```

## Отстраняване на проблеми

### Често срещани проблеми

**1. Филтрите не се създават**
- Проверете дали OCFilter интеграцията е активна
- Уверете се, че продуктите имат атрибути в XML файла
- Проверете дали продуктите са свързани с категории

**2. Твърде много атрибути**
- Намалете лимита в настройките
- Проверете качеството на данните от доставчика
- Филтрирайте ненужните атрибути

**3. Дублирани опции**
- Нормализацията може да създаде еднакви имена
- Проверете логовете за подробности
- При нужда изчистете OCFilter таблиците и рестартирайте

### Проверка на базата данни

За проверка на създадените филтри:

```sql
-- Брой създадени опции
SELECT COUNT(*) FROM oc_ocfilter_option;

-- Опции с техните имена
SELECT oo.option_id, ood.name, COUNT(ovtp.product_id) as products_count
FROM oc_ocfilter_option oo
LEFT JOIN oc_ocfilter_option_description ood ON oo.option_id = ood.option_id
LEFT JOIN oc_ocfilter_option_value_to_product ovtp ON oo.option_id = ovtp.option_id
GROUP BY oo.option_id, ood.name;

-- Продукти с атрибути
SELECT p.product_id, pd.name, COUNT(ovtp.option_id) as attributes_count
FROM oc_product p
LEFT JOIN oc_product_description pd ON p.product_id = pd.product_id
LEFT JOIN oc_ocfilter_option_value_to_product ovtp ON p.product_id = ovtp.product_id
GROUP BY p.product_id, pd.name
HAVING attributes_count > 0;
```

## Поддръжка и актуализации

### Регулярна поддръжка
- Проверявайте логовете за грешки
- Мониторирайте производителността при големи синхронизации
- Периодично почиствайте неизползвани опции

### Бъдещи подобрения
- Автоматично определяне на типа филтър
- Ръчно мапиране на атрибути
- Статистики за използването на филтрите
- Групиране на атрибути по категории

## Заключение

OCFilter интеграцията осигурява автоматично създаване на филтри за продуктите, което значително подобрява потребителското изживяване в онлайн магазина. Правилната конфигурация и мониторинг гарантират оптимална производителност и точност на филтрите.
