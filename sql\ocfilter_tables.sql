-- SQL за създаване на OCFilter таблици за Multi Feed Syncer интеграция
-- Тези таблици се създават автоматично от OCFilter модула, но са включени тук за справка

-- Основна таблица за филтърните опции (атрибути)
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option` (
  `option_id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) NOT NULL DEFAULT 'checkbox',
  `keyword` varchar(64) NOT NULL DEFAULT '',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int(3) NOT NULL DEFAULT '0',
  PRIMARY KEY (`option_id`),
  KEY `keyword` (`keyword`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Описания на опциите по езици
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_description` (
  `option_id` int(11) NOT NULL,
  `language_id` tinyint(4) NOT NULL,
  `name` varchar(128) NOT NULL DEFAULT '',
  `postfix` varchar(32) NOT NULL DEFAULT '',
  `description` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`option_id`,`language_id`),
  KEY `name` (`name`),
  KEY `language_id` (`language_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Стойности на опциите
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_value` (
  `value_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `option_id` int(11) NOT NULL,
  `keyword` varchar(64) NOT NULL DEFAULT '',
  `sort_order` int(3) NOT NULL DEFAULT '0',
  PRIMARY KEY (`value_id`),
  KEY `option_id` (`option_id`),
  KEY `keyword` (`keyword`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Описания на стойностите по езици
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_value_description` (
  `value_id` bigint(20) NOT NULL,
  `option_id` int(11) NOT NULL,
  `language_id` tinyint(4) NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`value_id`,`option_id`,`language_id`),
  KEY `option_id` (`option_id`),
  KEY `language_id` (`language_id`),
  KEY `name` (`name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Връзка между стойности и продукти
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_value_to_product` (
  `product_id` int(11) NOT NULL,
  `option_id` int(11) NOT NULL,
  `value_id` bigint(20) NOT NULL,
  `slide_value_min` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `slide_value_max` decimal(15,4) NOT NULL DEFAULT '0.0000',
  PRIMARY KEY (`product_id`,`option_id`,`value_id`),
  KEY `option_id` (`option_id`),
  KEY `value_id` (`value_id`),
  KEY `product_id` (`product_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Връзка между опции и категории
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_to_category` (
  `option_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  PRIMARY KEY (`option_id`,`category_id`),
  KEY `category_id` (`category_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Връзка между опции и магазини
CREATE TABLE IF NOT EXISTS `oc_ocfilter_option_to_store` (
  `option_id` int(11) NOT NULL,
  `store_id` int(11) NOT NULL,
  PRIMARY KEY (`option_id`,`store_id`),
  KEY `store_id` (`store_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Индекси за оптимизация на производителността
ALTER TABLE `oc_ocfilter_option_value_to_product` 
ADD INDEX `idx_product_option` (`product_id`, `option_id`),
ADD INDEX `idx_option_value` (`option_id`, `value_id`);

ALTER TABLE `oc_ocfilter_option_value_description` 
ADD INDEX `idx_option_language` (`option_id`, `language_id`),
ADD INDEX `idx_name_search` (`name`(50));

ALTER TABLE `oc_ocfilter_option_description` 
ADD INDEX `idx_option_language` (`option_id`, `language_id`),
ADD INDEX `idx_name_search` (`name`(50));
