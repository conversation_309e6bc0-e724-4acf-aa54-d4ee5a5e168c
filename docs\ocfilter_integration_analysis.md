# OCFilter и Multi Feed Syncer Интеграция - Технически Анализ

## 1. Ана<PERSON>из на OCFilter модула

### Структура на базата данни:

#### Основни таблици:
- **`oc_ocfilter_option`** - Основна таблица за филтърните опции (атрибути)
  - `option_id` (INT, AUTO_INCREMENT) - Уникален идентификатор
  - `type` (VARCHAR) - Тип на филтъра: 'checkbox', 'radio', 'slide', 'slide_dual'
  - `keyword` (VARCHAR) - SEO ключова дума
  - `status` (TINYINT) - Активен/неактивен статус
  - `sort_order` (INT) - Ред на сортиране

- **`oc_ocfilter_option_description`** - Опи<PERSON>ания на опциите по езици
  - `option_id` (INT) - Връзка към основната таблица
  - `language_id` (TINYINT) - ID на езика
  - `name` (VARCHAR) - Име на атрибута
  - `postfix` (VARCHAR) - Суфикс (напр. единици)
  - `description` (VARCHAR) - Описание

- **`oc_ocfilter_option_value`** - Стойности на опциите
  - `value_id` (BIGINT, AUTO_INCREMENT) - Уникален идентификатор на стойността
  - `option_id` (INT) - Връзка към опцията
  - `keyword` (VARCHAR) - SEO ключова дума за стойността
  - `sort_order` (INT) - Ред на сортиране

- **`oc_ocfilter_option_value_description`** - Описания на стойностите по езици
  - `value_id` (BIGINT) - Връзка към стойността
  - `option_id` (INT) - Връзка към опцията
  - `language_id` (TINYINT) - ID на езика
  - `name` (VARCHAR) - Име на стойността

- **`oc_ocfilter_option_value_to_product`** - Връзка между стойности и продукти
  - `product_id` (INT) - ID на продукта
  - `option_id` (INT) - ID на опцията
  - `value_id` (BIGINT) - ID на стойността
  - `slide_value_min` (DECIMAL) - Минимална стойност за slide филтри
  - `slide_value_max` (DECIMAL) - Максимална стойност за slide филтри

- **`oc_ocfilter_option_to_category`** - Връзка между опции и категории
  - `option_id` (INT) - ID на опцията
  - `category_id` (INT) - ID на категорията

### Как работи OCFilter:
1. **Създаване на атрибути**: Атрибутите се създават като "опции" с уникални имена
2. **Стойности на атрибутите**: Всяка опция може да има множество стойности
3. **Връзка с продукти**: Продуктите се свързват със стойности чрез таблицата `option_value_to_product`
4. **Категорийна връзка**: Опциите се асоциират с категории за показване в съответните филтри
5. **Типове филтри**: Поддържа checkbox, radio, slide и slide_dual типове

## 2. Анализ на Multi Feed Syncer модела

### Текущо състояние:
- **Извличане на атрибути**: Конекторите (напр. eOffice) извличат атрибути в `attributes_data_source` масив
- **Формат на данните**: 
  ```php
  'attributes_data_source' => [
      ['name' => 'Format_A47', 'value' => 'ATX 3.1'],
      ['name' => 'razmeri__sh__d__v___mm', 'value' => '150 x 140 x 86 mm'],
      // ...
  ]
  ```
- **Липсваща функционалност**: Няма методи за обработка и записване на атрибутите в OCFilter формат

### Методи за обновяване:
- `_processProductsToInsert()` - Добавя нови продукти
- `_processProductsToUpdate()` - Обновява съществуващи продукти  
- `_batchInsertProducts()` - Пакетно вмъкване
- `_batchUpdateProducts()` - Пакетно обновяване

## 3. Интеграционен план

### Етап 1: Създаване на методи за OCFilter интеграция

#### 3.1. Метод за обработка на атрибути при добавяне на продукти
```php
private function _processProductAttributes($product_id, $attributes_data, $category_ids, $mfsc_id)
```

#### 3.2. Метод за намиране или създаване на OCFilter опции
```php
private function _getOrCreateOCFilterOption($attribute_name, $category_ids)
```

#### 3.3. Метод за намиране или създаване на стойности на опции
```php
private function _getOrCreateOCFilterOptionValue($option_id, $attribute_value)
```

#### 3.4. Метод за свързване на продукт със стойности
```php
private function _linkProductToOCFilterValues($product_id, $option_value_mappings)
```

### Етап 2: Модификация на съществуващи методи

#### 3.5. Обновяване на `_batchInsertProducts()`
- Добавяне на обработка на атрибути след вмъкване на продуктите
- Извикване на `_processProductAttributes()` за всеки нов продукт

#### 3.6. Обновяване на `_batchUpdateProducts()`
- Добавяне на логика за обновяване на атрибути при промяна
- Опционално изтриване на стари атрибути и добавяне на нови

### Етап 3: Конфигурация и оптимизация

#### 3.7. Кеширащ механизъм
- Кеш за съществуващи OCFilter опции
- Кеш за стойности на опциите
- Намаляване на SQL заявките

#### 3.8. Конфигурационни опции
- Включване/изключване на OCFilter интеграцията
- Избор на типове атрибути за обработка
- Лимити за брой атрибути на продукт

## 4. Технически детайли за имплементация

### 4.1. Таблици за използване:
- `{{prefix}}ocfilter_option` - Създаване на нови атрибути
- `{{prefix}}ocfilter_option_description` - Имена на атрибутите
- `{{prefix}}ocfilter_option_value` - Стойности на атрибутите  
- `{{prefix}}ocfilter_option_value_description` - Имена на стойностите
- `{{prefix}}ocfilter_option_value_to_product` - Връзка продукт-атрибут
- `{{prefix}}ocfilter_option_to_category` - Връзка атрибут-категория

### 4.2. Алгоритъм за обработка:

1. **За всеки продукт с атрибути:**
   - Извличане на `attributes_data_source` данните
   - Определяне на категориите на продукта
   
2. **За всеки атрибут:**
   - Нормализиране на името (премахване на специални символи)
   - Проверка дали съществува OCFilter опция с това име
   - Ако не съществува - създаване на нова опция
   - Свързване на опцията с категориите на продукта
   
3. **За всяка стойност на атрибут:**
   - Проверка дали съществува стойност за тази опция
   - Ако не съществува - създаване на нова стойност
   - Свързване на продукта със стойността

### 4.3. Пример за SQL заявки:

#### Създаване на нова опция:
```sql
INSERT INTO {{prefix}}ocfilter_option 
SET type = 'checkbox', status = '1', sort_order = '0'
```

#### Създаване на описание на опция:
```sql
INSERT INTO {{prefix}}ocfilter_option_description 
SET option_id = '{$option_id}', language_id = '{$language_id}', 
    name = '{$attribute_name}'
```

#### Свързване на продукт със стойност:
```sql
INSERT IGNORE INTO {{prefix}}ocfilter_option_value_to_product 
SET product_id = '{$product_id}', option_id = '{$option_id}', 
    value_id = '{$value_id}'
```

## 5. Предимства на интеграцията

1. **Автоматично филтриране**: Продуктите автоматично стават филтрируеми по атрибутите от XML
2. **Съвместимост**: Пълна съвместимост с OCFilter модула
3. **Производителност**: Оптимизирани SQL заявки за пакетна обработка
4. **Гъвкавост**: Възможност за конфигуриране на типовете атрибути
5. **Поддръжка**: Автоматично обновяване при промени в source данните

## 6. Конкретни методи за имплементация

### 6.1. Основен метод за обработка на атрибути

```php
/**
 * Обработва атрибутите на продукт за OCFilter интеграция
 * @param int $product_id ID на продукта
 * @param array $attributes_data Масив с атрибути от source данните
 * @param array $category_ids Масив с ID-та на категориите на продукта
 * @param int $mfsc_id ID на конектора
 */
private function _processProductAttributes($product_id, $attributes_data, $category_ids, $mfsc_id) {
    if (empty($attributes_data) || empty($category_ids)) {
        return;
    }

    $language_id = (int)$this->config->get('config_language_id');
    $option_value_mappings = [];

    foreach ($attributes_data as $attribute) {
        if (empty($attribute['name']) || empty($attribute['value'])) {
            continue;
        }

        // Нормализиране на името на атрибута
        $normalized_name = $this->_normalizeAttributeName($attribute['name']);

        // Намиране или създаване на OCFilter опция
        $option_id = $this->_getOrCreateOCFilterOption($normalized_name, $category_ids, $language_id);

        if ($option_id) {
            // Намиране или създаване на стойност за опцията
            $value_id = $this->_getOrCreateOCFilterOptionValue($option_id, $attribute['value'], $language_id);

            if ($value_id) {
                $option_value_mappings[] = [
                    'option_id' => $option_id,
                    'value_id' => $value_id
                ];
            }
        }
    }

    // Свързване на продукта със стойностите
    if (!empty($option_value_mappings)) {
        $this->_linkProductToOCFilterValues($product_id, $option_value_mappings);
    }
}
```

### 6.2. Метод за нормализиране на имена на атрибути

```php
/**
 * Нормализира името на атрибут за използване в OCFilter
 * @param string $attribute_name Оригинално име на атрибута
 * @return string Нормализирано име
 */
private function _normalizeAttributeName($attribute_name) {
    // Премахване на специални символи и подчертавки
    $normalized = str_replace(['__', '_'], ' ', $attribute_name);

    // Премахване на цифри в началото/края
    $normalized = preg_replace('/^[A-Z]?\d+_?|_?\d+$/', '', $normalized);

    // Капитализиране на първата буква
    $normalized = ucfirst(trim($normalized));

    // Ограничаване на дължината
    if (mb_strlen($normalized) > 100) {
        $normalized = mb_substr($normalized, 0, 100);
    }

    return $normalized;
}
```

### 6.3. Метод за намиране/създаване на OCFilter опции

```php
/**
 * Намира или създава OCFilter опция
 * @param string $attribute_name Име на атрибута
 * @param array $category_ids Масив с ID-та на категориите
 * @param int $language_id ID на езика
 * @return int|false ID на опцията или false при грешка
 */
private function _getOrCreateOCFilterOption($attribute_name, $category_ids, $language_id) {
    // Проверка в кеша
    $cache_key = md5($attribute_name . '_' . implode(',', $category_ids));
    if (isset($this->ocfilter_options_cache[$cache_key])) {
        return $this->ocfilter_options_cache[$cache_key];
    }

    // Търсене на съществуваща опция
    $query = $this->_executeQuery("
        SELECT oo.option_id
        FROM `" . DB_PREFIX . "ocfilter_option` oo
        LEFT JOIN `" . DB_PREFIX . "ocfilter_option_description` ood
            ON (oo.option_id = ood.option_id)
        WHERE ood.name = '" . $this->db->escape($attribute_name) . "'
            AND ood.language_id = '" . (int)$language_id . "'
        LIMIT 1
    ");

    if ($query->num_rows) {
        $option_id = $query->row['option_id'];
        $this->ocfilter_options_cache[$cache_key] = $option_id;

        // Свързване с категориите, ако не е свързана
        $this->_linkOptionToCategories($option_id, $category_ids);

        return $option_id;
    }

    // Създаване на нова опция
    $this->_executeQuery("
        INSERT INTO `" . DB_PREFIX . "ocfilter_option`
        SET `type` = 'checkbox',
            `status` = '1',
            `sort_order` = '0',
            `keyword` = '" . $this->db->escape($this->_generateKeyword($attribute_name)) . "'
    ");

    $option_id = $this->_getLastId();

    if ($option_id) {
        // Създаване на описанието
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "ocfilter_option_description`
            SET `option_id` = '" . (int)$option_id . "',
                `language_id` = '" . (int)$language_id . "',
                `name` = '" . $this->db->escape($attribute_name) . "'
        ");

        // Свързване с категориите
        $this->_linkOptionToCategories($option_id, $category_ids);

        // Свързване с всички магазини
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "ocfilter_option_to_store`
            SET `option_id` = '" . (int)$option_id . "', `store_id` = '0'
        ");

        $this->ocfilter_options_cache[$cache_key] = $option_id;
        return $option_id;
    }

    return false;
}
```

### 6.4. Метод за свързване на опции с категории

```php
/**
 * Свързва OCFilter опция с категории
 * @param int $option_id ID на опцията
 * @param array $category_ids Масив с ID-та на категориите
 */
private function _linkOptionToCategories($option_id, $category_ids) {
    foreach ($category_ids as $category_id) {
        $this->_executeQuery("
            INSERT IGNORE INTO `" . DB_PREFIX . "ocfilter_option_to_category`
            SET `option_id` = '" . (int)$option_id . "',
                `category_id` = '" . (int)$category_id . "'
        ");
    }
}
```

### 6.5. Метод за намиране/създаване на стойности

```php
/**
 * Намира или създава стойност за OCFilter опция
 * @param int $option_id ID на опцията
 * @param string $attribute_value Стойност на атрибута
 * @param int $language_id ID на езика
 * @return int|false ID на стойността или false при грешка
 */
private function _getOrCreateOCFilterOptionValue($option_id, $attribute_value, $language_id) {
    // Нормализиране на стойността
    $normalized_value = trim($attribute_value);
    if (mb_strlen($normalized_value) > 255) {
        $normalized_value = mb_substr($normalized_value, 0, 255);
    }

    // Проверка в кеша
    $cache_key = $option_id . '_' . md5($normalized_value);
    if (isset($this->ocfilter_values_cache[$cache_key])) {
        return $this->ocfilter_values_cache[$cache_key];
    }

    // Търсене на съществуваща стойност
    $query = $this->_executeQuery("
        SELECT value_id
        FROM `" . DB_PREFIX . "ocfilter_option_value_description`
        WHERE option_id = '" . (int)$option_id . "'
            AND name = '" . $this->db->escape($normalized_value) . "'
            AND language_id = '" . (int)$language_id . "'
        LIMIT 1
    ");

    if ($query->num_rows) {
        $value_id = $query->row['value_id'];
        $this->ocfilter_values_cache[$cache_key] = $value_id;
        return $value_id;
    }

    // Създаване на нова стойност
    $this->_executeQuery("
        INSERT INTO `" . DB_PREFIX . "ocfilter_option_value`
        SET `option_id` = '" . (int)$option_id . "',
            `sort_order` = '0',
            `keyword` = '" . $this->db->escape($this->_generateKeyword($normalized_value)) . "'
    ");

    $value_id = $this->_getLastId();

    if ($value_id) {
        // Създаване на описанието
        $this->_executeQuery("
            INSERT INTO `" . DB_PREFIX . "ocfilter_option_value_description`
            SET `value_id` = '" . (int)$value_id . "',
                `option_id` = '" . (int)$option_id . "',
                `language_id` = '" . (int)$language_id . "',
                `name` = '" . $this->db->escape($normalized_value) . "'
        ");

        $this->ocfilter_values_cache[$cache_key] = $value_id;
        return $value_id;
    }

    return false;
}
```

## 7. Интеграция в съществуващите методи

### 7.1. Модификация на `_batchInsertProducts()`

Добавяне на обработка на атрибути след вмъкване на продуктите:

```php
// След успешното вмъкване на продуктите
foreach ($inserted_products_map as $sku => $product_id) {
    $product_data = $original_data_chunk[$sku];

    // Обработка на атрибути, ако има такива
    if (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
        // Получаване на категориите на продукта
        $category_ids = $this->_getProductCategoryIds($product_id, $product_data);

        if (!empty($category_ids)) {
            $this->_processProductAttributes(
                $product_id,
                $product_data['attributes_data_source'],
                $category_ids,
                $mfsc_id
            );
        }
    }
}
```

### 7.2. Модификация на `_batchUpdateProducts()`

Добавяне на обновяване на атрибути при промяна:

```php
// След успешното обновяване на продуктите
foreach ($products_to_update_map as $product_id => $product_data) {
    // Обработка на атрибути, ако има такива
    if (isset($product_data['attributes_data_source']) && !empty($product_data['attributes_data_source'])) {
        // Изтриване на стари OCFilter връзки за този продукт
        $this->_executeQuery("
            DELETE FROM `" . DB_PREFIX . "ocfilter_option_value_to_product`
            WHERE product_id = '" . (int)$product_id . "'
        ");

        // Получаване на категориите на продукта
        $category_ids = $this->_getProductCategoryIds($product_id, $product_data);

        if (!empty($category_ids)) {
            $this->_processProductAttributes(
                $product_id,
                $product_data['attributes_data_source'],
                $category_ids,
                $mfsc_id
            );
        }
    }
}
```

## 8. Помощни методи

### 8.1. Метод за свързване на продукт със стойности

```php
/**
 * Свързва продукт с OCFilter стойности
 * @param int $product_id ID на продукта
 * @param array $option_value_mappings Масив с option_id и value_id
 */
private function _linkProductToOCFilterValues($product_id, $option_value_mappings) {
    if (empty($option_value_mappings)) {
        return;
    }

    $values_to_insert = [];
    foreach ($option_value_mappings as $mapping) {
        $values_to_insert[] = "(
            '" . (int)$product_id . "',
            '" . (int)$mapping['option_id'] . "',
            '" . (int)$mapping['value_id'] . "',
            '0.0000',
            '0.0000'
        )";
    }

    if (!empty($values_to_insert)) {
        $this->_executeQuery("
            INSERT IGNORE INTO `" . DB_PREFIX . "ocfilter_option_value_to_product`
            (product_id, option_id, value_id, slide_value_min, slide_value_max)
            VALUES " . implode(', ', $values_to_insert)
        );
    }
}
```

### 8.2. Метод за генериране на SEO keywords

```php
/**
 * Генерира SEO keyword от текст
 * @param string $text Входен текст
 * @return string SEO keyword
 */
private function _generateKeyword($text) {
    // Конвертиране в малки букви
    $keyword = mb_strtolower($text, 'UTF-8');

    // Транслитерация на кирилица
    $keyword = $this->_transliterate($keyword);

    // Премахване на специални символи
    $keyword = preg_replace('/[^a-z0-9\s\-]/', '', $keyword);

    // Заместване на интервали с тирета
    $keyword = preg_replace('/\s+/', '-', trim($keyword));

    // Премахване на множествени тирета
    $keyword = preg_replace('/-+/', '-', $keyword);

    // Премахване на тирета в началото и края
    $keyword = trim($keyword, '-');

    return $keyword;
}
```

### 8.3. Метод за транслитерация

```php
/**
 * Транслитерира кирилски текст в латински
 * @param string $text Входен текст
 * @return string Транслитериран текст
 */
private function _transliterate($text) {
    $replace = [
        'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd',
        'е' => 'e', 'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y',
        'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o',
        'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u',
        'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch', 'ш' => 'sh',
        'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya'
    ];

    return strtr($text, $replace);
}
```

### 8.4. Метод за получаване на категории на продукт

```php
/**
 * Получава ID-тата на категориите за продукт
 * @param int $product_id ID на продукта
 * @param array $product_data Данни на продукта
 * @return array Масив с category_id
 */
private function _getProductCategoryIds($product_id, $product_data) {
    $category_ids = [];

    // Ако продуктът вече съществува, вземаме категориите от базата
    if ($product_id && !isset($product_data['categories_data_source'])) {
        $query = $this->_executeQuery("
            SELECT category_id
            FROM `" . DB_PREFIX . "product_to_category`
            WHERE product_id = '" . (int)$product_id . "'
        ");

        if ($query->rows) {
            foreach ($query->rows as $row) {
                $category_ids[] = $row['category_id'];
            }
        }
    }

    // Ако има категории от source данните, използваме тях
    if (isset($product_data['categories_data_source']) && !empty($product_data['categories_data_source'])) {
        // Тук трябва да се имплементира логика за получаване на category_id
        // от categories_data_source (вероятно чрез съществуващия механизъм за категории)
        // За момента връщаме празен масив, но това трябва да се доработи
    }

    return $category_ids;
}
```

### 8.5. Инициализация на кеш променливи

Добавяне в конструктора на класа:

```php
/**
 * Кеш за OCFilter опции и стойности
 */
private $ocfilter_options_cache = [];
private $ocfilter_values_cache = [];

public function __construct($registry) {
    parent::__construct($registry);
    $this->log = new Log('multi_feed_syncer.log');

    // Инициализация на кеш променливите
    $this->ocfilter_options_cache = [];
    $this->ocfilter_values_cache = [];
}
```

## 9. Конфигурационни опции

### 9.1. Добавяне на настройки в admin интерфейса

```php
// В контролера на Multi Feed Syncer
$data['ocfilter_integration_enabled'] = $this->config->get('multi_feed_syncer_ocfilter_enabled');
$data['ocfilter_max_attributes_per_product'] = $this->config->get('multi_feed_syncer_ocfilter_max_attributes') ?: 50;
$data['ocfilter_attribute_types'] = $this->config->get('multi_feed_syncer_ocfilter_types') ?: ['checkbox'];
```

### 9.2. Проверка на конфигурацията

```php
/**
 * Проверява дали OCFilter интеграцията е активна
 * @return bool
 */
private function _isOCFilterIntegrationEnabled() {
    return (bool)$this->config->get('multi_feed_syncer_ocfilter_enabled');
}
```

## 10. Заключение и препоръки

### Основни предимства:
1. **Автоматизация**: Пълна автоматизация на процеса за създаване на филтри
2. **Съвместимост**: 100% съвместимост с OCFilter модула
3. **Производителност**: Оптимизирани SQL заявки и кеширащ механизъм
4. **Гъвкавост**: Конфигурируеми опции за различни типове атрибути
5. **Поддръжка**: Лесна поддръжка и разширяване

### Стъпки за имплементация:
1. **Етап 1**: Добавяне на основните методи за OCFilter интеграция
2. **Етап 2**: Модификация на съществуващите методи за синхронизация
3. **Етап 3**: Добавяне на конфигурационни опции в admin интерфейса
4. **Етап 4**: Тестване с реални данни от XML файловете
5. **Етап 5**: Оптимизация на производителността

### Технически съображения:
- Използване на транзакции за осигуряване на целостта на данните
- Имплементиране на error handling за всички критични операции
- Добавяне на подробно логване за debugging
- Възможност за batch обработка на големи обеми данни
- Поддръжка на множество езици

### Бъдещи подобрения:
- Автоматично определяне на типа филтър според данните (slide за числа)
- Интелигентно групиране на атрибути по категории
- Възможност за ръчно мапиране на атрибути
- Статистики за използването на филтрите

## Статус на имплементацията

### ✅ Завършени компоненти:

1. **Основни методи в модела** (`admin/model/extension/module/multi_feed_syncer.php`):
   - `_processProductAttributes()` - основен метод за обработка на атрибути
   - `_normalizeAttributeName()` - нормализиране на имена на атрибути
   - `_getOrCreateOCFilterOption()` - намиране/създаване на опции
   - `_getOrCreateOCFilterOptionValue()` - намиране/създаване на стойности
   - `_linkProductToOCFilterValues()` - свързване продукт-атрибути
   - `_generateKeyword()` - генериране на SEO keywords
   - `_transliterate()` - транслитерация на кирилица
   - `_getProductCategoryIds()` - получаване на категории на продукт
   - `_linkOptionToCategories()` - свързване опции с категории

2. **Интеграция в съществуващи методи**:
   - Модифициран `_batchInsertProducts()` за обработка на атрибути при добавяне
   - Модифициран `_batchUpdateProducts()` за обновяване на атрибути

3. **Конфигурационни опции в контролера** (`admin/controller/extension/module/multi_feed_syncer.php`):
   - Добавени настройки за OCFilter интеграция в `index()` метода
   - Създаден `saveSettings()` метод за запазване на настройки
   - Добавена проверка за права и валидация

4. **Езикови променливи** (`admin/language/en-gb/extension/module/multi_feed_syncer.php`):
   - Добавени всички необходими текстове за интерфейса
   - Помощни текстове за обяснение на функционалността

5. **SQL структура** (`sql/ocfilter_tables.sql`):
   - Пълна SQL схема за OCFilter таблиците
   - Оптимизирани индекси за производителност

6. **Кеширащ механизъм**:
   - Кеш променливи за опции и стойности
   - Оптимизация на SQL заявките

### 🔄 Следващи стъпки за завършване:

1. **Обновяване на admin интерфейса**:
   - Добавяне на таб "Настройки" в шаблона
   - Форма за конфигуриране на OCFilter опциите
   - JavaScript за AJAX запазване на настройки

2. **Тестване**:
   - Тестване с реални XML данни от eOffice конектора
   - Проверка на създаването на филтри в OCFilter
   - Валидация на производителността

3. **Документация**:
   - Ръководство за потребителя
   - Технически документи за разработчици

### 📋 Конфигурационни опции:

- **`multi_feed_syncer_ocfilter_enabled`** (bool): Активира/деактивира интеграцията
- **`multi_feed_syncer_ocfilter_max_attributes`** (int): Максимален брой атрибути на продукт (1-200)

### 🔧 Как работи интеграцията:

1. **При добавяне на нови продукти**:
   - Извличат се атрибути от `attributes_data_source`
   - Нормализират се имената на атрибутите
   - Създават се OCFilter опции (ако не съществуват)
   - Създават се стойности за опциите
   - Продуктите се свързват със стойностите

2. **При обновяване на продукти**:
   - Изтриват се стари OCFilter връзки
   - Добавят се нови атрибути според актуалните данни

3. **Оптимизации**:
   - Кеширане на опции и стойности
   - Пакетни SQL операции
   - Ограничаване на броя атрибути

### 🎯 Предимства на имплементацията:

- **Автоматизация**: Пълна автоматизация без ръчна намеса
- **Производителност**: Оптимизирани SQL заявки и кеширане
- **Гъвкавост**: Конфигурируеми опции за различни нужди
- **Съвместимост**: 100% съвместимост с OCFilter модула
- **Поддръжка**: Лесна поддръжка и разширяване
